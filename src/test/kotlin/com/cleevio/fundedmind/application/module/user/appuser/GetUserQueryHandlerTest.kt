package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.appuser.query.GetUserQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetUserQueryHandlerTest @Autowired constructor(
    private val underTest: GetUserQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return user after sign up`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudentForOnboarding(id = it.id)
        }

        val result = underTest.handle(GetUserQuery(1.toUUID()))

        result.run {
            userId shouldBe 1.toUUID()
            createdAt shouldNotBe null
            email shouldBe "<EMAIL>"
            role shouldBe UserRole.STUDENT
            onboardingFinished shouldBe false
        }
    }

    @Test
    fun `should return user after onboarding finished - student entity exists`() {
        dataHelper.getAppUser(id = 1.toUUID(), email = "<EMAIL>")
        dataHelper.getStudent(id = 1.toUUID())

        val result = underTest.handle(GetUserQuery(1.toUUID()))

        result.run {
            userId shouldBe 1.toUUID()
            onboardingFinished shouldBe true
        }
    }

    @Test
    fun `should return admin user`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            userRole = UserRole.ADMIN,
        )

        val result = underTest.handle(GetUserQuery(1.toUUID()))

        result.run {
            userId shouldBe 1.toUUID()
            role shouldBe UserRole.ADMIN
            email shouldBe "<EMAIL>"
            onboardingFinished shouldBe false
        }
    }

    @Test
    fun `should return trader user`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            userRole = UserRole.TRADER,
        )
        dataHelper.getTrader(id = 1.toUUID())

        val result = underTest.handle(GetUserQuery(1.toUUID()))

        result.run {
            userId shouldBe 1.toUUID()
            role shouldBe UserRole.TRADER
            email shouldBe "<EMAIL>"
            onboardingFinished shouldBe false
        }
    }
}
